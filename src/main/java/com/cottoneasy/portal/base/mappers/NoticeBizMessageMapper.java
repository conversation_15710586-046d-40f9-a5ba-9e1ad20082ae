package com.cottoneasy.portal.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import com.cottoneasy.portal.base.vo.NoticeBizMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 业务通知主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Mapper
public interface NoticeBizMessageMapper extends BaseMapper<NoticeBizMessageEntity> {

    List<NoticeBizMessageVo> getNoticeBizMessageList(Long bizConfigId);

    /**
     * 查询已存在的批次号
     * @param batchNumbers 待查询的批次号
     * @return 已存在的批次号
     */
    List<String> getExistBatchNumbers(@Param("batchNumbers") Collection<String> batchNumbers);
}
