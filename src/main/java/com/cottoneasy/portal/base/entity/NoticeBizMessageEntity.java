package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 业务通知主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("de_sys_notice_biz_message")
@ApiModel(value="NoticeBizMessageEntity对象", description="业务通知主表")
public class NoticeBizMessageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId("ID")
    private Long id;

    @ApiModelProperty(value = "业务配置ID")
    @TableField("BIZ_CONFIG_ID")
    private Long bizConfigId;

    @ApiModelProperty(value = "系统编码")
    @TableField("APP_CODE")
    private String appCode;

    @ApiModelProperty(value = "关联业务id")
    @TableField("BUSINESS_ID")
    private String businessId;

    @ApiModelProperty(value = "处理状态：0-未处理，1-已处理")
    @TableField("DISPOSE_STATUS")
    private Integer disposeStatus;

    @ApiModelProperty(value = "处理时间")
    @TableField("DISPOSE_TIME")
    private LocalDateTime disposeTime;

    @ApiModelProperty(value = "删除状态 0-未删除，1-已删除")
    @TableField("DEL_FLAG")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("CREATE_USER_ID")
    private String createUserId;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @ApiModelProperty(value = "更新人ID")
    @TableField("UPDATE_USER_ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("NOTE")
    private String note;

    @ApiModelProperty(value = "参数")
    @TableField("PARAMS")
    private String params;


}
