package com.cottoneasy.portal.todo;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.entity.PopupConfigEntity;
import com.cottoneasy.portal.todo.vo.NoticeBizMessageViewVo;
import com.cottoneasy.portal.todo.vo.NoticeBizVO;
import com.cottoneasy.portal.todo.vo.PopupConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.cache.ICache;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.cottoneasy.portal.todo.TodoConstants.*;

@Component
@Slf4j
public class TodoCacheService {

    private final ICache todoItemCache;

    private final ICache popupConfigCache;

    private final ICache bizConfigCache;

    public TodoCacheService(@Qualifier(REGION_TODO) ICache todoItemCache,
                            @Qualifier(REGION_POPUP_CONFIG) ICache popupConfigCache,
                            @Qualifier(REGION_BIZ_CONFIG) ICache bizConfigCache) {
        this.todoItemCache = todoItemCache;
        this.popupConfigCache = popupConfigCache;
        this.bizConfigCache = bizConfigCache;
    }

    public NoticeBizMessageViewVo get(String noticeObjectCode, TodoConstants.BIZ_PROCESS_TYPE bizType, TodoConstants.NOTICE_TYPE noticeType) {

        String redisKey = TodoConstants.buildTodoCacheKey(noticeObjectCode, bizType, noticeType);

        return todoItemCache.get(redisKey, JSONObject.class)
                // 获取到则返序列化为对象
                .map(obj -> obj.toJavaObject(NoticeBizMessageViewVo.class))
                // 没获取到则返回空列表
                .orElse(null);
    }

    public void put(String noticeObjectCode, TodoConstants.BIZ_PROCESS_TYPE bizType, TodoConstants.NOTICE_TYPE noticeType, NoticeBizMessageViewVo noticeContent) {

        String redisKey = TodoConstants.buildTodoCacheKey(noticeObjectCode, bizType, noticeType);

        if (Objects.nonNull(noticeContent)) {
            todoItemCache.put(redisKey, noticeContent);
        } else {
            todoItemCache.evict(redisKey);
        }
    }

    public List<NoticeBizMessageViewVo> getNoticeBizMessageViewVoList(String noticeObjectCode) {
        String redisKey = TodoConstants.buildNoticeObjectKey(noticeObjectCode);
        Optional<Collection<String>> messageViewsStrOption = todoItemCache.fuzzyVulues(redisKey);
        if (messageViewsStrOption.isPresent()) {
            JSONArray jsonArray = JSONUtil.parseArray(messageViewsStrOption.get());
            return jsonArray.toList(NoticeBizMessageViewVo.class);
        }
        return Collections.emptyList();
    }

    public NoticeBizEntity getNoticeBizConfig(String bizCode) {
        Optional<Object> o = bizConfigCache.get(bizCode);
        if (o.isPresent()) {
            JSONObject jsonObject = (JSONObject) o.get();
            return jsonObject.toJavaObject(NoticeBizEntity.class);
        }
        return null;
    }

    public void putNoticeBizConfig(NoticeBizEntity noticeBizEntity) {
        if (noticeBizEntity != null && noticeBizEntity.getNoticeBizCode() != null) {
            bizConfigCache.put(noticeBizEntity.getNoticeBizCode(), noticeBizEntity);
        }
    }

    public NoticeBizVO getNoticeBizConfigVO(String bizCode) {
        Optional<Object> o = bizConfigCache.get(bizCode);
        if (o.isPresent()) {
            JSONObject jsonObject = (JSONObject) o.get();
            return jsonObject.toJavaObject(NoticeBizVO.class);
        }
        return null;
    }

    public PopupConfigVO getPopupConfig(Long configId) {
        Optional<Object> o = popupConfigCache.get(configId.toString());
        if (o.isPresent()) {
            JSONObject jsonObject = (JSONObject) o.get();
            return jsonObject.toJavaObject(PopupConfigVO.class);
        }
        return null;
    }

    public void putPopupConfig(PopupConfigEntity popupConfigEntity) {
        if (popupConfigEntity != null && popupConfigEntity.getConfigId() != null) {
            popupConfigCache.put(popupConfigEntity.getConfigId().toString(), popupConfigEntity);
        }
    }
}