package com.cottoneasy.portal.todo;


import com.cottoneasy.portal.todo.model.AbstractTodoModel;
import com.cottoneasy.portal.todo.model.TodoContext;
import com.cottoneasy.portal.todo.model.TodoModelBuilder;
import com.cottoneasy.portal.todo.model.TodoItem;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.message.response.annonation.ResponseDataSet;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 测试用
 */
@Service("todo.addTodo.1")
@ApiRequestObject(value = "添加待办", name = "AddTodoParam", groups = {"待办"})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "addTodo", desc = "登录页面，check展示名称", type = AbstractTodoModel.class,multipart = true)

})
@ResponseDataSet
@RequiredArgsConstructor
public class AddTodoService implements IBusinessService {

	private final TodoManageService todoManageService;

	private final TodoModelBuilder todoModelBuilder;

	@Override
	public void doVerify(ServiceHandlerContext context) {

	}

	@Override
	public void doWork(ServiceHandlerContext context) {
		// 构造待办上下文
		TodoContext todoContext = new TodoContext();

		// 系统编码
		todoContext.setAppCode(RuntimeContext.getSystemCode());
		// 通知对象编码
		todoContext.setTargetObjectCode("016000");
		// 创建人信息
		todoContext.setCreateUserId(context.getCurrentLoginName());
		// 创建人名称
		todoContext.setCreateUserName(context.getCurrentUserRealName());

		Map<String, String> params = new HashMap<>();
		params.put("applicationNo", "1,2,3,4,5");
		todoContext.setParams(params);

		String hrefUrl = "https://www.baidu.com";
		String operatorTitle = "背书";

		List<TodoItem> contextItems = new ArrayList<>();
		for (int i = 0; i < 3; i++) {
			TodoItem item = new TodoItem();
			// 业务id
			item.setBizId(String.valueOf(i));
			// 批次号
			item.setBatchNumber("batchNumber-" + i);
			item.setHrefUrl(hrefUrl);
			item.setOperatorTitle(operatorTitle);
			contextItems.add(item);
		}
		todoContext.setContextItems(contextItems);

		// 交易商质押确认
		todoManageService.addTodo(todoModelBuilder.createWarehousePledgeSupplementaryConfirm(todoContext));

		createSuccessResponse(context);
		context.getResponseBody().getDataSet().put("result", true);
	}
}
