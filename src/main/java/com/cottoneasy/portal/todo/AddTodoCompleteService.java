package com.cottoneasy.portal.todo;


import com.cottoneasy.portal.todo.model.AbstractTodoModel;
import com.cottoneasy.portal.todo.model.TodoContext;
import com.cottoneasy.portal.todo.model.TodoModelBuilder;
import com.cottoneasy.portal.todo.model.TodoItem;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.message.response.annonation.ResponseDataSet;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @ClassName: GetLoginAgreement
 * @Description: 获取登录协议
 * <AUTHOR>
 * @date 2023年2月6日 上午11:53:35
 *
 */
@Service("todo.addTodoComplete.1")
@ApiRequestObject(value = "完成待办", name = "addTodoComplete", groups = {"待办"})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "addTodoComplete", type = AbstractTodoModel.class,multipart = true)

})
@ResponseDataSet
@RequiredArgsConstructor
public class AddTodoCompleteService implements IBusinessService {

	private final TodoManageService todoManageService;

	private final TodoModelBuilder todoModelBuilder;

	@Override
	public void doVerify(ServiceHandlerContext context) {

	}

	@Override
	public void doWork(ServiceHandlerContext context) {
		// 构造待办上下文
		TodoContext todoContext = new TodoContext();

		// 系统编码
		todoContext.setAppCode(RuntimeContext.getSystemCode());
		// 通知对象编码
		todoContext.setTargetObjectCode("016000");
		// 创建人信息
		todoContext.setCreateUserId(context.getCurrentLoginName());
		// 创建人名称
		todoContext.setCreateUserName(context.getCurrentUserRealName());

		String hrefUrl = "https://www.baidu.com";
		String operatorTitle = "背书";

		Map<String, String> params = new HashMap<>();
		params.put("applicationNo", "4,5");
		todoContext.setParams(params);

		List<TodoItem> contextItems = new ArrayList<>();
		for (int i = 0; i < 2; i++) {
			TodoItem item = new TodoItem();
			// 业务id
			item.setBizId(String.valueOf(i));
			// 批次号
			item.setBatchNumber("batchNumber-" + i);
			item.setHrefUrl(hrefUrl);
			item.setOperatorTitle(operatorTitle);
			contextItems.add(item);
		}
		todoContext.setContextItems(contextItems);

		// 交易商质押确认
		todoManageService.completeTodo(todoModelBuilder.createWarehousePledgeSupervisionCompletion(todoContext));

		createSuccessResponse(context);
		context.getResponseBody().getDataSet().put("result", true);
	}
}
