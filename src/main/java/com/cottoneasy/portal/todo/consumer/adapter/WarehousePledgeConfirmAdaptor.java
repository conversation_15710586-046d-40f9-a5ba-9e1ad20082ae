package com.cottoneasy.portal.todo.consumer.adapter;

import com.cottoneasy.portal.todo.TodoConstants;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;
import com.cottoneasy.portal.todo.model.impl.WarehousePledgeConfirmModel;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@RequiredArgsConstructor
public class WarehousePledgeConfirmAdaptor implements BatchNumberUrlArchiveCacheableView {

    private final WarehousePledgeConfirmModel todo;

    @Override
    public String getGroupingKey() {
        return todo.getNoticeObjectCode();
    }

    @Override
    public String getBizId() {
        return todo.getBizId();
    }

    public TodoConstants.BIZ_PROCESS_TYPE getBizProcessType() {
        return todo.getBizProcessType();
    }

    @Override
    public String getFieldValue() {
        return todo.getBatchNumber();
    }

    @Override
    public String getOperateTitle() {
        return todo.getOperatorTitle();
    }

    @Override
    public String getUrlTemplate() {
        return todo.getHrefUrl();
    }

    @Override
    public TodoConstants.NOTICE_TYPE getNoticeType() {
        return todo.getNoticeType();
    }

    @Override
    public TodoConstants.NOTICE_OBJECT_TYPE getNoticeObjectType() {
        return todo.getNoticeObjectType();
    }

    @Override
    public String getTargetObjectCode() {
        return todo.getNoticeObjectCode();
    }

    @Override
    public String getTargetObjectName() {
        return todo.getNoticeObjectCode();
    }

    @Override
    public String getAppCode() {
        return todo.getAppCode();
    }

    @Override
    public String getCreateUserName() {
        return todo.getCreateUserName();
    }

    @Override
    public String getCreateUserId() {
        return todo.getCreateUserId();
    }

    @Override
    public LocalDateTime getCreateTime() {
        return todo.getCreateTime();
    }

    @Override
    public Map<String, String> getParamsMap() {
        return todo.getParams();
    }

    @Override
    public String getDataType() {
        return todo.getNoticeType().getNoticeTypeName();
    }
}
