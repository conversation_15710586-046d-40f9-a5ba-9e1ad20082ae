package com.cottoneasy.portal.todo.consumer.view;

import com.cottoneasy.portal.todo.TodoConstants;


/**
 * 添加待办归档缓存视图接口。
 */
public interface BatchNumberUrlArchiveCacheableView extends BatchNumberUrlCacheableView {

    /**
     * 获取创建待办通知对象类型
     */
    TodoConstants.NOTICE_OBJECT_TYPE getNoticeObjectType();

    /**
     * 获取创建待办通知对象代码
     */
    String getTargetObjectName();

    /**
     * 获取创建待办应用编码, 例如交易中心的应用编码
     */
    String getAppCode();

    /**
     * 获取待办数据类型
     */
    String getDataType();
}
