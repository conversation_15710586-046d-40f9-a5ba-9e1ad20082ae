package com.cottoneasy.portal.todo;


import com.cottoneasy.portal.todo.vo.NoticeBizMessageViewVo;
import com.cottoneasy.portal.todo.vo.NoticeBizVO;
import com.cottoneasy.portal.todo.vo.PopupConfigVO;
import com.cottoneasy.portal.todo.vo.TodoVo;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.core.message.response.annonation.ResponseDataSet;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("todo.getTodoList.1")
@ApiRequestObject(value = "获取待办列表", name = "GetTodoListParam", groups = {"待办"})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result", type = TodoVo.class, desc = "返回结果", multipart = true),
})
@ResponseDataSet
@RequiredArgsConstructor
public class GetTodoListService implements IBusinessService {

    private final TodoCacheService todoCacheService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String noticeObjectCode = context.getCurrentUserCustomCode();
        List<NoticeBizMessageViewVo> noticeBizMessageViewVoList = todoCacheService.getNoticeBizMessageViewVoList(noticeObjectCode);

        Map<String, List<NoticeBizMessageViewVo>> configTodoListMap = noticeBizMessageViewVoList.stream()
                .collect(Collectors.groupingBy(t -> t.getBizProcessConfigId() + "," + t.getNoticeType()));

        List<TodoVo> result = configTodoListMap.entrySet().stream()
                .map(entry -> {
                    String noticeTypeCode = entry.getKey().split(",")[1];

                    NoticeBizVO noticeBizVO = todoCacheService.getNoticeBizConfigVO(noticeTypeCode);
                    PopupConfigVO popupConfig = todoCacheService.getPopupConfig(noticeBizVO.getNoticeConfigId());

                    return new TodoVo(noticeBizVO, popupConfig, entry.getValue());
                })
                .collect(Collectors.toList());

        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", result);

    }
}
