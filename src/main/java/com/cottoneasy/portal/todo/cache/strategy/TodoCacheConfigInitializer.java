package com.cottoneasy.portal.todo.cache.strategy;

import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.entity.PopupConfigEntity;
import com.cottoneasy.portal.base.service.NoticeBizService;
import com.cottoneasy.portal.base.service.PopupConfigService;
import com.cottoneasy.portal.todo.TodoCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * 待办事项缓存配置初始化器
 * 负责在应用启动时初始化弹窗配置和业务通知配置缓存
 */
@Component
@Slf4j
public class TodoCacheConfigInitializer {

    @Resource
    private PopupConfigService popupConfigService;

    @Resource
    private NoticeBizService noticeBizService;

    @Resource
    private TodoCacheService todoCacheService;

    /**
     * 初始化配置信息
     * 在Spring容器初始化完成后自动执行
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化待办事项缓存配置");
        try {
            // 初始化待办提醒的配置信息
            this.initPopupConfig();
            // 初始化业务通知配置
            this.initBizConfig();
            log.info("待办事项缓存配置初始化完成");
        } catch (Exception e) {
            log.error("待办事项缓存配置初始化失败", e);
            throw new RuntimeException("待办事项缓存配置初始化失败", e);
        }
    }

    /**
     * 初始化弹窗配置缓存
     */
    private void initPopupConfig() {
        try {
            List<PopupConfigEntity> popupConfigList = this.popupConfigService.getValidConfig();
            if (popupConfigList != null) {
                for (PopupConfigEntity popupConfig : popupConfigList) {
                    String key = popupConfig.getConfigId().toString();
                    // 通过TodoCacheService访问缓存，保持缓存访问的一致性
                    todoCacheService.putPopupConfig(popupConfig);
                    log.debug("初始化弹窗配置缓存: {}", key);
                }
                log.info("成功初始化 {} 个弹窗配置", popupConfigList.size());
            } else {
                log.info("没有找到有效的弹窗配置");
            }
        } catch (Exception e) {
            log.error("初始化弹窗配置缓存失败", e);
            throw new RuntimeException("初始化弹窗配置缓存失败", e);
        }
    }

    /**
     * 初始化业务通知配置缓存
     */
    private void initBizConfig() {
        try {
            List<NoticeBizEntity> bizConfigList = this.noticeBizService.getAllConfig();
            if (bizConfigList != null && !bizConfigList.isEmpty()) {
                for (NoticeBizEntity bizConfig : bizConfigList) {
                    String key = bizConfig.getNoticeBizCode();
                    // 通过TodoCacheService访问缓存，保持缓存访问的一致性
                    todoCacheService.putNoticeBizConfig(bizConfig);
                    log.debug("初始化业务通知配置缓存: {}", key);
                }
                log.info("成功初始化 {} 个业务通知配置", bizConfigList.size());
            } else {
                log.info("没有找到业务通知配置");
            }
        } catch (Exception e) {
            log.error("初始化业务通知配置缓存失败", e);
            throw new RuntimeException("初始化业务通知配置缓存失败", e);
        }
    }
}
