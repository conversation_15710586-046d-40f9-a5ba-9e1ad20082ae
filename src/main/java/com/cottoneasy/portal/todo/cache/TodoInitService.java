package com.cottoneasy.portal.todo.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cottoneasy.portal.base.entity.*;
import com.cottoneasy.portal.base.mappers.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 启动时，同步 redis 缓存
 * 负责在应用启动时初始化待办事项缓存
 */
@Component
@RequiredArgsConstructor
public class TodoInitService {

    private final NoticeBizMessageMapper noticeBizMessageMapper;
    private final NoticeContentMapper noticeContentMapper;
    private final NoticeObjectMapper noticeObjectMapper;
    private final TodoCacheInitializationHelper cacheInitializationHelper;

    /**
     * 启动时，同步缓存
     * 在Spring容器初始化完成后自动执行
     */
    @PostConstruct
    public void init() {
        // 获取所有未处理的待办事项
        List<NoticeBizMessageEntity> allNotDisposedTodos = this.getAllNotDisposedTodos();
        if (allNotDisposedTodos.isEmpty()) {
            return;
        }

        // 提取所有未处理待办事项的ID
        List<Long> notDisposedMessageIds = allNotDisposedTodos.stream()
                .map(NoticeBizMessageEntity::getId)
                .collect(Collectors.toList());

        // 获取待办事项相关的内容和对象信息
        Map<Long, List<NoticeContentEntity>> noticeContentMap = this.getNoticeContentsByBizMessageId(notDisposedMessageIds);
        List<NoticeObjectEntity> noticeObjects = this.getNoticeObjectByBizMessageId(notDisposedMessageIds);

        // 委托给辅助类处理缓存初始化
        cacheInitializationHelper.initializeCache(allNotDisposedTodos, noticeContentMap, noticeObjects);
    }

    /**
     * 获取所有未处理的待办事项
     * 处理状态为0表示未处理
     *
     * @return 未处理的待办事项列表
     */
    public List<NoticeBizMessageEntity> getAllNotDisposedTodos() {
        LambdaQueryWrapper<NoticeBizMessageEntity> qw = new LambdaQueryWrapper<>();
        int notDisposed = 0;
        qw.eq(NoticeBizMessageEntity::getDisposeStatus, notDisposed);
        return noticeBizMessageMapper.selectList(qw);
    }

    /**
     * 根据待办事项ID列表获取相关的内容信息
     *
     * @param bizMessageIds 待办事项ID列表
     * @return 按待办事项ID分组的内容信息映射
     */
    public Map<Long, List<NoticeContentEntity>> getNoticeContentsByBizMessageId(List<Long> bizMessageIds) {
        LambdaQueryWrapper<NoticeContentEntity> qw = new LambdaQueryWrapper<>();
        qw.in(NoticeContentEntity::getBizMessageId, bizMessageIds);
        return noticeContentMapper.selectList(qw).stream()
                .collect(Collectors.groupingBy(NoticeContentEntity::getBizMessageId));
    }

    /**
     * 根据待办事项ID列表获取相关的通知对象信息
     *
     * @param bizMessageIds 待办事项ID列表
     * @return 通知对象列表
     */
    private List<NoticeObjectEntity> getNoticeObjectByBizMessageId(List<Long> bizMessageIds) {
        LambdaQueryWrapper<NoticeObjectEntity> qw = new LambdaQueryWrapper<>();
        qw.in(NoticeObjectEntity::getBizMessageId, bizMessageIds);
        return noticeObjectMapper.selectList(qw);
    }
}
