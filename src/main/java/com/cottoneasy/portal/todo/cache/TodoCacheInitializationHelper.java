package com.cottoneasy.portal.todo.cache;

import com.cottoneasy.portal.base.entity.*;
import com.cottoneasy.portal.base.mappers.NoticeBizMapper;
import com.cottoneasy.portal.todo.TodoCacheService;
import com.cottoneasy.portal.todo.vo.NoticeBizMessageViewVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.cottoneasy.portal.todo.TodoConstants.*;

/**
 * 待办事项缓存初始化辅助类
 * 负责处理缓存初始化的核心业务逻辑
 */
@Component
@RequiredArgsConstructor
public class TodoCacheInitializationHelper {

    private final NoticeBizMapper noticeBizMapper;
    private final TodoCacheService todoCacheService;

    /**
     * 初始化缓存，处理所有未完成的待办事项
     *
     * @param allNotDisposedTodos 所有未处理的待办事项
     * @param noticeContentMap 待办事项内容映射
     * @param noticeObjects 通知对象列表
     */
    public void initializeCache(List<NoticeBizMessageEntity> allNotDisposedTodos,
                                Map<Long, List<NoticeContentEntity>> noticeContentMap,
                                List<NoticeObjectEntity> noticeObjects) {

        // 按照通知对象编码分组, 通知对象编码 -> 通知消息 ID 列表
        Map<String, Set<Long>> byNoticeObjectCode = noticeObjects.stream()
                .collect(Collectors.groupingBy(NoticeObjectEntity::getNoticeObjectCode,
                        Collectors.mapping(NoticeObjectEntity::getBizMessageId, Collectors.toSet())));

        // 处理每个通知对象
        for (Map.Entry<String, Set<Long>> entry : byNoticeObjectCode.entrySet()) {
            String noticeObjectCode = entry.getKey();
            Set<Long> bizMessageIds = entry.getValue();

            // 过滤出当前通知对象的待办事项
            List<NoticeBizMessageEntity> bizMessageList = allNotDisposedTodos.stream()
                    .filter(t -> bizMessageIds.contains(t.getId()))
                    .collect(Collectors.toList());

            processNoticeObject(noticeObjectCode, bizMessageList, noticeContentMap);
        }
    }

    /**
     * 处理单个通知对象的所有待办事项
     *
     * @param noticeObjectCode 通知对象编码
     * @param bizMessageList 当前通知对象的待办事项列表
     * @param noticeContentMap 待办事项内容映射
     */
    private void processNoticeObject(String noticeObjectCode,
                                     List<NoticeBizMessageEntity> bizMessageList,
                                     Map<Long, List<NoticeContentEntity>> noticeContentMap) {

        // 按业务配置ID对当前通知对象的待办事项进行分组
        // 修复：使用bizMessageList而不是allNotDisposedTodos进行分组
        Map<Long, List<NoticeBizMessageEntity>> byBiz = bizMessageList.stream()
                .collect(Collectors.groupingBy(NoticeBizMessageEntity::getBizConfigId));

        // 处理每个业务分组
        for (Map.Entry<Long, List<NoticeBizMessageEntity>> messageEntry : byBiz.entrySet()) {
            Long bizConfigId = messageEntry.getKey();
            List<NoticeBizMessageEntity> messageList = messageEntry.getValue();

            processBizGroup(noticeObjectCode, bizConfigId, messageList, noticeContentMap);
        }
    }

    /**
     * 处理单个业务分组的待办事项
     *
     * @param noticeObjectCode 通知对象编码
     * @param bizConfigId 业务配置ID
     * @param messageList 当前业务分组的待办事项列表
     * @param noticeContentMap 待办事项内容映射
     */
    private void processBizGroup(String noticeObjectCode,
                                 Long bizConfigId,
                                 List<NoticeBizMessageEntity> messageList,
                                 Map<Long, List<NoticeContentEntity>> noticeContentMap) {

        // 提取当前业务分组的所有通知内容
        List<NoticeContentEntity> noticeContents = extractNoticeContents(messageList, noticeContentMap);

        // 获取操作标题
        String operateTitle = noticeContents.stream()
                .filter(c -> Objects.equals(c.getPopupFiledKey(), "operate"))
                .findFirst()
                .map(NoticeContentEntity::getPopupFiledValue)
                .orElse("");
        // 获取操作标题
        String url = noticeContents.stream()
                .filter(c -> Objects.equals(c.getPopupFiledKey(), "operate"))
                .findFirst()
                .map(NoticeContentEntity::getAHrefUrl)
                .orElse("");

        // 收集所有未处理的批次号
        Set<String> allNotDisposedBatchNumbers = noticeContents.stream()
                .filter(c -> Objects.equals(c.getPopupFiledKey(), "batchNumber"))
                .map(NoticeContentEntity::getPopupFiledValue)
                .collect(Collectors.toSet());

        // 获取业务配置信息
        NoticeBizEntity bizConfig = noticeBizMapper.selectById(bizConfigId);
        // 目前业务类型与通知配置的枚举值一样
        NOTICE_TYPE noticeType = NOTICE_TYPE.parseName(bizConfig.getNoticeBizCode());
        BIZ_PROCESS_TYPE bizType = BIZ_PROCESS_TYPE.parseName(bizConfig.getNoticeBizCode());

        // 获取当前通知对象下，当前业务类型的通知
        NoticeBizMessageViewVo currentView = todoCacheService.get(noticeObjectCode, bizType, noticeType);

        // 创建或更新缓存条目
        createOrUpdateCacheEntry(noticeObjectCode, noticeType, bizType, currentView,
                               operateTitle, allNotDisposedBatchNumbers, url);
    }

    /**
     * 提取待办事项内容
     *
     * @param messageList 待办事项列表
     * @param noticeContentMap 待办事项内容映射
     * @return 所有待办事项的内容列表
     */
    private List<NoticeContentEntity> extractNoticeContents(List<NoticeBizMessageEntity> messageList,
                                                            Map<Long, List<NoticeContentEntity>> noticeContentMap) {
        return messageList.stream()
                .flatMap(m -> noticeContentMap.getOrDefault(m.getId(), Collections.emptyList()).stream())
                .collect(Collectors.toList());
    }

    /**
     * 创建或更新缓存条目
     *
     * @param noticeObjectCode 通知对象编码
     * @param noticeType 通知类型
     * @param bizType 业务类型
     * @param currentView 当前缓存视图
     * @param operateTitle 操作标题
     * @param allNotDisposedBatchNumbers 所有未处理的批次号
     */
    private void createOrUpdateCacheEntry(String noticeObjectCode,
                                          NOTICE_TYPE noticeType,
                                          BIZ_PROCESS_TYPE bizType,
                                          NoticeBizMessageViewVo currentView,
                                          String operateTitle,
                                          Set<String> allNotDisposedBatchNumbers,
                                          String url) {

        if (Objects.isNull(currentView)) {
            // 如果缓存中不存在，则创建新的视图对象
            currentView = new NoticeBizMessageViewVo();
            currentView.setNoticeType(noticeType.toString());
            currentView.setBizProcessConfigId(bizType.getBizConfigCode());
            currentView.setCustomCode(noticeObjectCode);
            currentView.setCustomName(noticeObjectCode);
            currentView.setOperateTitle(operateTitle);
            currentView.setUrl(url);
            currentView.setBatchNumbers(String.join(BATCH_NUMBER_SPLITTER, allNotDisposedBatchNumbers));
        } else {
            // 如果缓存中已存在，则合并批次号
            Set<String> existingBatchNumbers = new HashSet<>(
                    Arrays.asList(currentView.getBatchNumbers().split(BATCH_NUMBER_SPLITTER)));
            existingBatchNumbers.addAll(allNotDisposedBatchNumbers);
            currentView.setBatchNumbers(String.join(BATCH_NUMBER_SPLITTER, existingBatchNumbers));
            currentView.setUrl(url);
        }

        // 更新缓存
        todoCacheService.put(noticeObjectCode, bizType, noticeType, currentView);
    }
}
